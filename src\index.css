
@import url('https://fonts.googleapis.com/css2?family=Koulen&family=Manrope:wght@400;500;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 222 20% 8%;
    --foreground: 210 40% 94%;

    /* Updated theme colors: primary, accent and their foregrounds */
    --primary: 43 67% 75%;   /* #e6cb8e from screenshot */
    --primary-foreground: 40 14% 13%; /* #232323 */
    --accent: 43 67% 75%;    /* #e6cb8e */
    --accent-foreground: 40 14% 13%;

    --muted: 227 17% 16%;
    --muted-foreground: 220 14% 63%;

    --secondary: 222 17% 22%;
    --secondary-foreground: 210 40% 94%;

    --border: 223 14% 24%;
    --input: 223 14% 24%;
    --ring: 43 67% 75%;
    --card: 222.2 15% 12%;
    --card-foreground: 210 40% 94%;

    /* Popover colors for dropdown menus */
    --popover: 222.2 15% 12%;
    --popover-foreground: 210 40% 94%;

    /* Destructive colors */
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 94%;

    --radius: 1rem;
  }

  .dark {
    --background: 0 0% 0%;          /* TRUE BLACK for dark theme */
    --foreground: 210 40% 94%;
    --card: 0 0% 3%;                /* Almost black for cards */
    --popover: 0 0% 3%;             /* Almost black for popovers */
    --popover-foreground: 210 40% 94%;
    --primary: 43 67% 75%; /* #e6cb8e for dark as well */
    --primary-foreground: 40 14% 13%;
    --accent: 43 67% 75%;
    --accent-foreground: 40 14% 13%;
    --border: 223 14% 24%;
    --input: 223 14% 24%;
    --ring: 43 67% 75%;
    --muted: 227 17% 16%;
    --muted-foreground: 220 14% 63%;
    --secondary: 222 17% 22%;
    --secondary-foreground: 210 40% 94%;
    --destructive: 0 62% 50%;
    --destructive-foreground: 210 40% 94%;
  }

  html {
    font-size: 87.5%; /* was 97%, now about 10% smaller */
    font-family: 'Manrope', Arial, sans-serif;
    background: hsl(var(--background));
    color: hsl(var(--foreground));
    min-height: 100vh;
  }

  body {
    @apply bg-background text-foreground min-h-screen font-manrope;
    letter-spacing: 0.02em;
    /* font-size handled by html */
  }

  a {
    @apply transition-colors duration-150;
  }

  h1, h2, h3, h4, .stdb-heading, .card-title, .main-nav {
    font-family: 'Koulen', Impact, Arial, sans-serif !important;
    font-weight: 400 !important;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    text-shadow: 0 2px 16px #1113, 0 1px 2px #0002;
  }

  .card-title {
    font-size: 1.17rem; /* was 1.27rem */
    margin-top: 0.58rem;
    margin-bottom: 0.18rem;
    display: block;
    line-height: 1.02;
    text-align: center;
    padding: 0.01em 0.06em;
    background: none !important;
    border-radius: 0 !important;
    color: hsl(var(--foreground));
    opacity: 0.99;
    text-shadow: 0 2px 14px #8be7b630, 0 1px 2px #0007;
  }

  .main-nav-link {
    font-family: 'Koulen', Impact, Arial, sans-serif !important;
    text-transform: uppercase;
    font-size: 1.10rem; /* was 1.28rem */
    letter-spacing: 0.03em;
    font-weight: 400;
    padding: 0.13em 0.32em;
    transition: color 0.14s, background 0.14s, box-shadow 0.14s;
    border-radius: 0.4em;
  }

  /* Stylish genres styling */
  .card-genres {
    font-size: 0.87rem;
    color: #e6cb8e !important;
    font-family: 'Manrope', Arial, sans-serif;
    font-weight: 600;
    letter-spacing: 0.009em;
    text-align: center;
    opacity: 0.90;
    margin-bottom: 0.04em;
    /* mimic the screenshot color and weight */
    text-shadow: 0 1px 2px #222c, 0 1px 6px #000a;
  }

  .card-year {
    font-size: 0.83rem; /* was 0.92rem */
    color: #f05723;
    font-family: 'Koulen', Impact, Arial, sans-serif;
    font-weight: 400;
    text-align: center;
    letter-spacing: 0.08em;
    opacity: 0.93;
    margin-bottom: 0.02em;
    text-shadow: 0 2px 10px #0001;
  }

  .streamdb-logo {
    filter: drop-shadow(0 2px 8px #19e950a5);
    height: 2.2rem !important; /* was 2.6rem */
    width: auto !important;
  }

  svg {
    stroke-width: 1.7 !important;
    width: 1em !important;
    height: 1em !important;
    vertical-align: middle;
  }

  /* New headline/section style for impact, especially h1, h2 */
  h1, h2 {
    font-size: 2.3rem; /* was 2.7rem */
    line-height: 1.01;
    color: #191917;
    text-shadow: 0 2px 16px #e3d1a180, 0 1px 2px #0002;
  }
}

/* CardShadow & Rounded, Section tweaks */
section {
  @apply rounded-2xl bg-card bg-opacity-85 shadow-[0_5px_32px_0_rgba(12,20,29,0.10)] p-6 mb-10 border border-border;
}

/* Grid card hover effect */
.card-grid-item,
.shadcn-card {
  @apply rounded-2xl bg-background/85 shadow-xl hover:shadow-2xl hover:scale-[1.035] transition border border-border;
  border-width: 1.3px;
  box-shadow: 0 7px 32px 0 #a67e4438;
}

/* Responsive for homepage sections */
@media (max-width: 900px) {
  section {
    @apply p-1.5 mb-5 rounded-xl;
  }
}

::-webkit-scrollbar {
  width: 7px;
  background: #22252b;
}
::-webkit-scrollbar-thumb {
  background: #e0ca8b88;
  border-radius: 8px;
}

/* Enhanced dropdown styling for dark theme consistency */
select {
  background-color: hsl(var(--card)) !important;
  color: hsl(var(--foreground)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  min-height: 2.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

select:hover {
  background-color: hsl(var(--muted)) !important;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

select:focus {
  outline: none;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2) !important;
  background-color: hsl(var(--card)) !important;
}

/* Enhanced option styling for better visibility */
select option {
  background-color: hsl(var(--popover)) !important;
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.75rem 1rem;
  border: none;
  font-weight: 500;
  line-height: 1.5;
  min-height: 2.5rem;
  cursor: pointer;
}

select option:hover,
select option:focus {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

select option:checked,
select option:selected {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

/* Radix UI Select component styling */
[data-radix-select-content] {
  background-color: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  padding: 0.25rem;
}

[data-radix-select-item] {
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  outline: none;
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
}

[data-radix-select-item]:hover,
[data-radix-select-item]:focus,
[data-radix-select-item][data-highlighted] {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

[data-radix-select-item][data-state="checked"] {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

[data-radix-select-item][data-disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* Dropdown menu component styling */
.dropdown-menu {
  background-color: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  z-index: 50;
  min-width: 8rem;
  overflow: hidden;
  padding: 0.25rem;
}

.dropdown-menu-item {
  color: hsl(var(--popover-foreground)) !important;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all 0.15s ease-in-out;
  outline: none;
  position: relative;
  display: flex;
  align-items: center;
  user-select: none;
}

.dropdown-menu-item:hover,
.dropdown-menu-item:focus {
  background-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-weight: 600;
}

.dropdown-menu-item:active {
  background-color: hsl(var(--primary) / 0.9) !important;
}

.dropdown-menu-item[data-disabled] {
  opacity: 0.5;
  pointer-events: none;
}

/* Mobile responsiveness for dropdowns */
@media (max-width: 640px) {
  select {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    min-height: 2.25rem;
  }

  select option {
    padding: 0.6rem 0.8rem;
    min-height: 2.25rem;
  }

  [data-radix-select-item],
  .dropdown-menu-item {
    padding: 0.4rem 0.6rem;
    font-size: 0.8rem;
  }
}

/* Enhanced content page animations and effects */
.content-page-hero {
  animation: fadeInUp 0.8s ease-out;
}

.content-page-card {
  animation: fadeInUp 0.6s ease-out;
  animation-fill-mode: both;
}

.content-page-card:nth-child(1) { animation-delay: 0.1s; }
.content-page-card:nth-child(2) { animation-delay: 0.2s; }
.content-page-card:nth-child(3) { animation-delay: 0.3s; }

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible {
  outline: 2px solid hsl(var(--primary));
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Backdrop blur support fallback */
@supports not (backdrop-filter: blur(12px)) {
  .backdrop-blur-sm {
    background-color: hsl(var(--card) / 0.95) !important;
  }
}

/* Mobile optimizations for content pages */
@media (max-width: 768px) {
  .content-page-hero {
    animation-duration: 0.6s;
  }

  .content-page-card {
    animation-duration: 0.4s;
  }

  /* Reduce motion for mobile performance */
  @media (prefers-reduced-motion: reduce) {
    .content-page-hero,
    .content-page-card {
      animation: none;
    }

    * {
      transition-duration: 0.1s !important;
    }
  }

  /* Touch-friendly sizing */
  .mobile-touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved mobile spacing */
  .mobile-spacing {
    padding: 1rem;
    margin-bottom: 1.5rem;
  }

  /* Mobile hero adjustments */
  .mobile-hero-title {
    font-size: 2.5rem !important;
    line-height: 1.1 !important;
    margin-bottom: 1rem !important;
  }

  .mobile-hero-description {
    font-size: 1rem !important;
    line-height: 1.5 !important;
  }
}

